import { BaseCommand } from "@core/base";

export default class Container extends BaseCommand<typeof Container> {
    static description = `Manage container deployments:
    [no arguments]: Show available container types`;
    static examples = [
        `$ clara container`,
        `$ clara container nes`,
        `$ clara container edoc`,
        `$ clara container certs`,
    ];

    public async process(): Promise<object> {
        this.app.info("Available container types:");
        this.app.info("  nes    - NES application container");
        this.app.info("  edoc   - Documentation (Docusaurus) container");
        this.app.info(
            "  certs  - Generate SSL certificates for local development"
        );
        this.app.info("");
        this.app.info(
            "Run 'clara container [type] --help' for more information on a specific container type."
        );

        return {
            message: "Available container types displayed",
            containers: ["nes", "edoc", "certs"],
        };
    }
}
