# Define a snippet containing the shared configuration
(shared_config) {
    import shared.docker
    import shared.static
}

# HTTP site on port 3080 (primary for production)
:3080 {
    import shared_config
}

# HTTPS site on port 3443 for wiki.local.clararx.com (optional, only if certs exist)
wiki.local.clararx.com:3443 {
    # Only use TLS if certificates are available
    tls /opt/clara/config/certs/wiki.local.clararx.com.pem /opt/clara/config/certs/wiki.local.clararx.com-key.pem {
        # This will fail gracefully if certs don't exist
    }
    import shared_config
}
