# NES Container Certificates - DEPRECATED

⚠️ **This directory is deprecated. Certificates are now managed centrally.**

## New Certificate Management

SSL certificates for all containers are now managed centrally in:
`bd/packages/containers/certs/`

### Generate Certificates

Use the Clara CLI to generate certificates:

```bash
# Generate certificates for all containers (including NES)
clara container certs

# This will create certificates in the centralized location
# and they will be automatically copied to containers during build
```

### Migration

This directory (`packages/containers/nes/config/certs/`) is no longer used.
All certificate generation and management should be done through the centralized system.

For more information, see: `bd/packages/containers/certs/README.md`