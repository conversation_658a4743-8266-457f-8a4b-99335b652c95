# About

**Clara CLI** is a command-line interface (CLI) tool designed to simplify the process of managing and interacting with the Clara stack. It is built using [oclif](https://oclif.io/) in TypeScript and provides a user-friendly interface for various tasks, such as compiling, running dev servers, DSL management, and various development operations.

## Requirements

You need to install pnpm with oclif and typescript packages globally to use this CLI tool. Follow the [Clara Wiki](https://admin.envoylabs.net/resource/docs/tools/Setting%20up%20pnpm) for more information.

## Table of Contents

<!-- toc -->
* [About](#about)
* [Usage](#usage)
* [Commands](#commands)
<!-- tocstop -->

# Usage

<!-- usage -->
```sh-session
$ npm install -g @clara/cli
$ clara COMMAND
running command...
$ clara (--version)
@clara/cli/1.1.0 darwin-arm64 node-v22.16.0
$ clara --help [COMMAND]
USAGE
  $ clara COMMAND
...
```
<!-- usagestop -->

# Commands

<!-- commands -->

<!-- commandsstop -->
